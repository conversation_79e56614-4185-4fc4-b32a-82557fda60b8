{"name": "n-admin", "type": "module", "engines": {"node": ">=18.15.0"}, "scripts": {"dev": "mist --host", "build": "vue-tsc && mist build", "clear:vercel": "rm -rf ./vercel.json", "build:vercel": "run-s clear:vercel build:nitro", "build:nitro": "mist build nitro", "start:nirto": "node .output/server/index.mjs", "preview": "mist preview", "lint": "eslint src --fix", "typecheck": "vue-tsc --noEmit", "bump:patch": "changelogen --bump --output CHANGELOG.md --release", "bump:minor": "changelogen --bump --output CHANGELOG.md --release --minor", "bump:major": "changelogen --bump --output CHANGELOG.md --release --major", "prepare": "husky", "dir-tree": "esno ./scripts/dir-tree", "gen:uno": "esno ./scripts/gen-unocss"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.32", "@antv/l7": "^2.22.1", "@ctrl/tinycolor": "^4.1.0", "@v-c/utils": "^0.0.26", "@vueuse/core": "^10.11.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.7", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.2.2", "sortablejs": "^1.15.6", "upyun": "^3.4.6", "vue": "^3.5.8", "vue-i18n": "^9.14.0", "vue-router": "^4.4.5", "vuedraggable": "^2.24.3"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@mistjs/cli": "0.0.1-beta.9", "@mistjs/vite-plugin-preload": "^0.0.1", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.16.5", "@types/treeify": "^1.0.3", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/test-utils": "^2.4.6", "antdv-component-resolver": "^1.0.7", "antdv-style": "0.0.1-beta.2", "changelogen": "^0.5.7", "cross-env": "^7.0.3", "directory-tree": "^3.5.2", "esbuild": "^0.20.2", "eslint": "^8.57.1", "esno": "^0.17.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "husky": "^9.1.6", "jsdom": "^22.1.0", "less": "^4.2.0", "lint-staged": "^14.0.1", "lodash": "^4.17.21", "nitropack": "^2.9.7", "npm-run-all": "^4.1.5", "picocolors": "^1.1.0", "treeify": "^1.1.0", "ts-node": "^10.9.2", "typescript": "~5.5.2", "unocss": "^0.57.7", "unocss-preset-chinese": "^0.3.3", "unocss-preset-ease": "^0.0.3", "unplugin-auto-import": "^0.16.7", "unplugin-config": "^0.1.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.7", "vitest": "^0.34.6", "vue-tsc": "^2.1.6"}, "lint-staged": {"**/*.{vue,ts,js,jsx,tsx}": "eslint src --fix"}}